@baseUrl = http://localhost:3000

### Test Replicate AI Service directly (no auth)
POST {{baseUrl}}/api/test/replicate-unified
Content-Type: application/json

{
  "prompt": "a beautiful sunset over mountains, photorealistic, high quality",
  "options": {
    "output_quality": 90,
    "output_format": "webp",
    "num_outputs": 1,
    "disable_safety_checker": true
  }
}

### Test through unified AI API with authentication
POST {{baseUrl}}/api/ai/generate
Content-Type: application/json
Authorization: Bearer sk-xxx

{
  "model": "black-forest-labs/flux-krea-dev",
  "type": "image",
  "prompt": "a beautiful sunset over mountains, photorealistic, high quality",
  "options": {
    "output_quality": 90,
    "output_format": "webp",
    "variants": 1,
    "disable_safety_checker": true
  }
}

### Test img2img functionality
POST {{baseUrl}}/api/ai/generate
Content-Type: application/json
Authorization: Bearer sk-xxx

{
  "model": "black-forest-labs/flux-krea-dev",
  "type": "image",
  "prompt": "transform this into a cyberpunk style artwork",
  "options": {
    "output_quality": 90,
    "output_format": "webp",
    "variants": 1,
    "uploadedImages": ["https://example.com/input-image.jpg"],
    "prompt_strength": 0.8
  }
}

### Test with multiple outputs
POST {{baseUrl}}/api/ai/generate
Content-Type: application/json
Authorization: Bearer sk-xxx

{
  "model": "black-forest-labs/flux-krea-dev",
  "type": "image",
  "prompt": "a cute cat sitting in a garden, photorealistic",
  "options": {
    "output_quality": 90,
    "output_format": "webp",
    "variants": 2,
    "guidance": 7.5,
    "num_inference_steps": 28
  }
}
