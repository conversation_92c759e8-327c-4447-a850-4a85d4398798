-- 激活 Replicate 模型
-- 将 black-forest-labs/flux-krea-dev 模型设置为活跃状态

UPDATE ai_models 
SET 
    is_active = true,
    api_endpoint = '/api/ai/generate'  -- 使用统一的 API 端点
WHERE 
    model_id = 'black-forest-labs/flux-krea-dev' 
    AND provider = 'replicate';

-- 验证更新结果
SELECT 
    model_id,
    model_name,
    model_type,
    provider,
    api_endpoint,
    credits_per_unit,
    is_active,
    supported_features
FROM ai_models 
WHERE provider = 'replicate';

-- 如果模型不存在，则插入
INSERT INTO ai_models (
    model_id, 
    model_name, 
    model_type, 
    provider, 
    api_endpoint, 
    credits_per_unit, 
    unit_type, 
    is_active,
    description_i18n,
    model_name_i18n,
    supported_features,
    max_input_size
) 
SELECT 
    'black-forest-labs/flux-krea-dev',
    'Flux Krea Dev',
    'image',
    'replicate',
    '/api/ai/generate',
    25,
    'images',
    true,
    '{"en": "An opinionated text-to-image model from Black Forest Labs in collaboration with Krea that excels in photorealism. Creates images that avoid the oversaturated AI look.", "zh": "来自 Black Forest Labs 与 Krea 合作的专业文本转图像模型，擅长生成逼真的照片效果，避免过度饱和的 AI 风格。"}',
    '{"en": "Flux Krea Dev", "zh": "Flux Krea 开发版"}',
    '["text2image", "img2img", "photorealism", "high_quality", "image_upload"]',
    2000
WHERE NOT EXISTS (
    SELECT 1 FROM ai_models 
    WHERE model_id = 'black-forest-labs/flux-krea-dev' 
    AND provider = 'replicate'
);

-- 最终验证
SELECT 
    '=== Replicate 模型状态 ===' as status,
    model_id,
    model_name,
    provider,
    is_active,
    credits_per_unit,
    supported_features
FROM ai_models 
WHERE provider = 'replicate';
